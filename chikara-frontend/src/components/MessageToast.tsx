import { DisplayAvatar } from "@/components/DisplayAvatar";
import { motion, AnimatePresence } from "framer-motion";
import { MessageCircle, ArrowRight } from "lucide-react";
import { TextAnimate } from "@/components/ui/text-animate";
import type { NavigateFunction } from "react-router-dom";

interface MessageToastProps {
    toastId: string;
    userData?: { username: string; avatar: string | null; [key: string]: unknown };
    onReply: (id: string, navigate: NavigateFunction) => void;
    navigate: NavigateFunction;
}

export const MessageToast = ({ toastId, userData, onReply, navigate }: MessageToastProps) => {
    return (
        <AnimatePresence mode="wait">
            <motion.div
                key={toastId}
                className="relative overflow-hidden rounded-xl border border-blue-500/30 backdrop-blur-xl bg-gradient-to-br from-blue-500/20 via-blue-600/10 to-blue-700/20 shadow-2xl shadow-black/20 cursor-pointer group hover:scale-105 transition-transform duration-200 max-w-md"
                initial={{
                    opacity: 0,
                    scale: 0.8,
                    y: -50,
                    filter: "blur(10px)",
                }}
                animate={{
                    opacity: 1,
                    scale: 1,
                    y: 0,
                    filter: "blur(0px)",
                }}
                exit={{
                    opacity: 0,
                    scale: 0.8,
                    x: 100,
                    filter: "blur(5px)",
                }}
                transition={{
                    type: "spring",
                    stiffness: 300,
                    damping: 25,
                    duration: 0.4,
                }}
                style={{
                    background: `linear-gradient(135deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.6) 100%)`,
                }}
            >
                {/* Shimmer effect overlay */}
                <div
                    className="absolute inset-0 opacity-30 group-hover:opacity-50 transition-opacity duration-300"
                    style={{
                        background: `linear-gradient(45deg, transparent 30%, #3b82f620 50%, transparent 70%)`,
                        backgroundSize: "200% 200%",
                        animation: "shiny-text 3s ease-in-out infinite",
                    }}
                />

                {/* Content */}
                <div className="relative z-10 flex items-center gap-4 p-4">
                    {/* Message Icon */}
                    <motion.div
                        initial={{ scale: 0, rotate: -180 }}
                        animate={{ scale: 1, rotate: 0 }}
                        className="flex-shrink-0 text-blue-400"
                        transition={{
                            delay: 0.1,
                            type: "spring",
                            stiffness: 400,
                            damping: 15,
                        }}
                    >
                        <MessageCircle size={20} />
                    </motion.div>

                    {/* Avatar */}
                    <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="flex-shrink-0"
                        transition={{
                            delay: 0.15,
                            type: "spring",
                            stiffness: 300,
                            damping: 20,
                        }}
                    >
                        <DisplayAvatar className="size-10 rounded-full ring-2 ring-blue-400/30" src={userData} />
                    </motion.div>

                    {/* Message Content */}
                    <div className="flex-1 min-w-0">
                        <TextAnimate
                            animation="slideUp"
                            by="word"
                            duration={0.4}
                            delay={0.2}
                            className="text-blue-400 text-sm font-semibold"
                            startOnView={false}
                        >
                            {userData?.username || "Unknown User"}
                        </TextAnimate>
                        <TextAnimate
                            animation="slideUp"
                            by="word"
                            duration={0.4}
                            delay={0.3}
                            className="text-white/80 text-xs mt-1"
                            startOnView={false}
                        >
                            Just sent you a message!
                        </TextAnimate>
                    </div>

                    {/* Action Button */}
                    <motion.button
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.4 }}
                        className="flex-shrink-0 flex items-center gap-1 px-3 py-1.5 bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 hover:text-blue-300 text-xs font-medium rounded-lg transition-all duration-200 border border-blue-500/30 hover:border-blue-400/50"
                        onClick={() => onReply(toastId, navigate)}
                    >
                        View
                        <ArrowRight size={12} />
                    </motion.button>
                </div>
            </motion.div>
        </AnimatePresence>
    );
};
