import { useEffect } from "react";
import { api } from "@/helpers/api";
import { useQueryClient } from "@tanstack/react-query";
import { useLocation, useNavigate } from "react-router-dom";
import { useNormalStore, useSessionStore, useSocketStore } from "../app/store/stores";
import { NotificationHandler, NotificationType } from "../components/NotificationHandler";

// Define types for socket notification message
interface NotificationMessage {
    type: NotificationType;
    details: string | Record<string, unknown>;
}

export const useNotificationHandler = () => {
    const { socket } = useSocketStore();
    const { setJustJailed } = useNormalStore();
    const { setLevelupValue } = useSessionStore();
    const queryClient = useQueryClient();
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        if (socket) {
            socket.on("notification", (msg: NotificationMessage) => {
                if (msg.type === "jail") {
                    setJustJailed(true);
                }
                if (msg.type === "message") {
                    if (location.pathname.includes("/inbox")) {
                        // If the user gets a message notification while they are on the inbox page,
                        // invalidate the chat history and return so that the toast is not shown
                        queryClient.invalidateQueries({
                            queryKey: api.messaging.getChatHistory.key(),
                        });
                        return;
                    }
                }

                queryClient.invalidateQueries({
                    queryKey: api.notifications.getList.key(),
                });

                // Handle the notification and display the toast
                NotificationHandler(msg.type, msg.details, navigate, queryClient, setLevelupValue);
            });
            return () => {
                socket.off("notification");
            };
        }
    }, [socket]);
};
